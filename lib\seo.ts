import { Metadata } from 'next'

export interface SEOConfig {
  title: string
  description: string
  keywords?: string[]
  image?: string
  url?: string
  type?: 'website' | 'article' | 'product' | 'organization'
  author?: string
  publishedTime?: string
  modifiedTime?: string
  section?: string
  tags?: string[]
  price?: number
  currency?: string
  availability?: string
  brand?: string
  category?: string
}

// Hreflang configuration for international SEO
export const hreflangRegions = [
  'x-default',
  'en-IE',
  'en-GB',
  'en-MT',
  'en-DK',
  'en-FI',
  'en-SE',
  'en-NO',
  'en-NL',
  'en-DE',
  'en-US',
  'en-CA',
  'en-CH',
  'en-IT',
  'en-ES',
  'en-PE',
  'en-AU',
  'en-PL',
  'en-NZ',
  'en-VE',
  'en-AR',
  'en-ZA',
  'en-NG',
  'en-KE',
  'en-RW'
] as const

export type HreflangRegion = typeof hreflangRegions[number]

export const defaultSEO = {
  siteName: 'Swift Africa Safaris',
  siteUrl: 'https://swiftafricasafaris.com',
  defaultTitle: 'Swift Africa Safaris - Premier African Safari Tours & Adventures',
  defaultDescription: 'Experience unforgettable African safari adventures with Swift Africa Safaris. Expert-guided tours across Rwanda, Tanzania, Uganda, and South Africa. Book your dream safari today!',
  defaultImage: '/images/common/swift-africa-safaris-best-tour-operator-icon.png',
  twitterHandle: '@SwiftAfricaSafaris',
  facebookPage: 'http://web.facebook.com/profile.php?id=61550743088246',
  instagramHandle: '@swiftafricasafaris',
  linkedinPage: 'https://linkedin.com/company/swift-africa-safaris'
}

/**
 * Generates hreflang tags for international SEO
 * @param currentPath - The current page path (e.g., '/about', '/package/safari-tour')
 * @returns Array of hreflang link objects for Next.js metadata
 */
export function generateHreflangTags(currentPath: string = '/') {
  // Ensure path starts with /
  const normalizedPath = currentPath.startsWith('/') ? currentPath : `/${currentPath}`

  return hreflangRegions.map(region => ({
    rel: 'alternate' as const,
    hrefLang: region,
    href: `${defaultSEO.siteUrl}${normalizedPath}`
  }))
}

export function generateMetadata(config: SEOConfig): Metadata {
  const {
    title,
    description,
    keywords = [],
    image = defaultSEO.defaultImage,
    url,
    type = 'website',
    author,
    publishedTime,
    modifiedTime,
    section,
    tags = []
  } = config

  const fullTitle = title.includes(defaultSEO.siteName) 
    ? title 
    : `${title} | ${defaultSEO.siteName}`
  
  const fullUrl = url ? `${defaultSEO.siteUrl}${url}` : defaultSEO.siteUrl
  const fullImage = image.startsWith('http') ? image : `${defaultSEO.siteUrl}${image}`

  const metadata: Metadata = {
    title: fullTitle,
    description,
    keywords: keywords.join(', '),
    authors: author ? [{ name: author }] : [{ name: defaultSEO.siteName }],
    creator: defaultSEO.siteName,
    publisher: defaultSEO.siteName,
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    openGraph: {
      type: type as 'website' | 'article',
      locale: 'en_US',
      url: fullUrl,
      title: fullTitle,
      description,
      siteName: defaultSEO.siteName,
      images: [
        {
          url: fullImage,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: fullTitle,
      description,
      images: [fullImage],
      creator: defaultSEO.twitterHandle,
      site: defaultSEO.twitterHandle,
    },
    alternates: {
      canonical: fullUrl,
      languages: generateHreflangTags(url || '/').reduce((acc, tag) => {
        acc[tag.hrefLang] = tag.href
        return acc
      }, {} as Record<string, string>)
    },
  }

  // Add article-specific metadata
  if (type === 'article' && publishedTime) {
    metadata.openGraph = {
      ...metadata.openGraph,
      type: 'article',
      publishedTime,
      modifiedTime,
      section,
      tags,
      authors: author ? [author] : undefined,
    }
  }

  return metadata
}

export function generateOrganizationSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'TravelAgency',
    name: defaultSEO.siteName,
    description: defaultSEO.defaultDescription,
    url: defaultSEO.siteUrl,
    logo: `${defaultSEO.siteUrl}/images/common/swift-africa-safaris-best-tour-operator-icon.png`,
    image: `${defaultSEO.siteUrl}${defaultSEO.defaultImage}`,
    telephone: '+250 788 123 456',
    email: '<EMAIL>',
    address: {
      '@type': 'PostalAddress',
      streetAddress: 'Kigali, Rwanda',
      addressLocality: 'Kigali',
      addressCountry: 'RW'
    },
    sameAs: [
      defaultSEO.facebookPage,
      defaultSEO.linkedinPage,
      `https://instagram.com/${defaultSEO.instagramHandle.replace('@', '')}`,
      `https://twitter.com/${defaultSEO.twitterHandle.replace('@', '')}`
    ],
    serviceArea: {
      '@type': 'Place',
      name: 'East Africa',
      description: 'Rwanda, Tanzania, Uganda, Kenya, South Africa'
    },
    priceRange: '$$-$$$',
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.9',
      reviewCount: '150',
      bestRating: '5',
      worstRating: '1'
    }
  }
}

export function generateWebsiteSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: defaultSEO.siteName,
    description: defaultSEO.defaultDescription,
    url: defaultSEO.siteUrl,
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${defaultSEO.siteUrl}/search?q={search_term_string}`
      },
      'query-input': 'required name=search_term_string'
    },
    publisher: {
      '@type': 'Organization',
      name: defaultSEO.siteName,
      logo: {
        '@type': 'ImageObject',
        url: `${defaultSEO.siteUrl}/images/common/swift-africa-safaris-best-tour-operator-icon.png`
      }
    }
  }
}

export function generateBreadcrumbSchema(items: Array<{ name: string; url: string }>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: `${defaultSEO.siteUrl}${item.url}`
    }))
  }
}

export function generateArticleSchema(config: {
  title: string
  description: string
  author: string
  publishedTime: string
  modifiedTime?: string
  image: string
  url: string
  tags?: string[]
}) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: config.title,
    description: config.description,
    image: config.image.startsWith('http') ? config.image : `${defaultSEO.siteUrl}${config.image}`,
    author: {
      '@type': 'Person',
      name: config.author
    },
    publisher: {
      '@type': 'Organization',
      name: defaultSEO.siteName,
      logo: {
        '@type': 'ImageObject',
        url: `${defaultSEO.siteUrl}/images/common/swift-africa-safaris-best-tour-operator-icon.png`
      }
    },
    datePublished: config.publishedTime,
    dateModified: config.modifiedTime || config.publishedTime,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `${defaultSEO.siteUrl}${config.url}`
    },
    keywords: config.tags?.join(', ')
  }
}

export function generateProductSchema(config: {
  name: string
  description: string
  image: string
  price: number
  currency: string
  availability: string
  brand?: string
  category?: string
  url: string
  rating?: number
  reviewCount?: number
}) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: config.name,
    description: config.description,
    image: config.image.startsWith('http') ? config.image : `${defaultSEO.siteUrl}${config.image}`,
    brand: {
      '@type': 'Brand',
      name: config.brand || defaultSEO.siteName
    },
    category: config.category,
    offers: {
      '@type': 'Offer',
      price: config.price,
      priceCurrency: config.currency,
      availability: `https://schema.org/${config.availability}`,
      seller: {
        '@type': 'Organization',
        name: defaultSEO.siteName
      },
      url: `${defaultSEO.siteUrl}${config.url}`
    },
    aggregateRating: config.rating ? {
      '@type': 'AggregateRating',
      ratingValue: config.rating,
      reviewCount: config.reviewCount || 1,
      bestRating: 5,
      worstRating: 1
    } : undefined
  }
}

export function generateTourSchema(config: {
  name: string
  description: string
  image: string
  price: number
  currency: string
  duration: string
  location: string
  url: string
  rating?: number
  reviewCount?: number
}) {
  return {
    '@context': 'https://schema.org',
    '@type': 'TouristTrip',
    name: config.name,
    description: config.description,
    image: config.image.startsWith('http') ? config.image : `${defaultSEO.siteUrl}${config.image}`,
    touristType: 'Safari Enthusiast',
    itinerary: {
      '@type': 'ItemList',
      name: `${config.name} Itinerary`,
      description: config.description
    },
    offers: {
      '@type': 'Offer',
      price: config.price,
      priceCurrency: config.currency,
      availability: 'https://schema.org/InStock',
      seller: {
        '@type': 'TravelAgency',
        name: defaultSEO.siteName
      },
      url: `${defaultSEO.siteUrl}${config.url}`
    },
    provider: {
      '@type': 'TravelAgency',
      name: defaultSEO.siteName,
      url: defaultSEO.siteUrl
    },
    location: {
      '@type': 'Place',
      name: config.location,
      address: {
        '@type': 'PostalAddress',
        addressCountry: 'East Africa'
      }
    },
    duration: config.duration,
    aggregateRating: config.rating ? {
      '@type': 'AggregateRating',
      ratingValue: config.rating,
      reviewCount: config.reviewCount || 1,
      bestRating: 5,
      worstRating: 1
    } : undefined
  }
}
