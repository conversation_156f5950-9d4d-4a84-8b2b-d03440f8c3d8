import { MetadataRoute } from 'next'
import { defaultSEO } from '@/lib/seo'

export default function robots(): MetadataRoute.Robots {
  return {
    rules: [
      {
        userAgent: '*',
        allow: '/',
        disallow: [
          '/admin/',
          '/partner/',
          '/api/',
          '/login',
          '/_next/',
          '/private/',
          '*.json',
        ],
      },
      {
        userAgent: 'Googlebot',
        allow: '/',
        disallow: [
          '/admin/',
          '/partner/',
          '/api/',
          '/login',
        ],
      },
    ],
    sitemap: `${defaultSEO.siteUrl}/sitemap.xml`,
    host: defaultSEO.siteUrl,
  }
}
